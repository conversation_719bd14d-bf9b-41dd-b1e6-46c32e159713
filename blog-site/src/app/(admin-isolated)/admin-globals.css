@tailwind base;
@tailwind components;
@tailwind utilities;

/* Complete CSS Reset for Admin Interface */
.admin-isolated {
  /* Reset all inherited styles */
  all: initial;
  
  /* Base font and display settings */
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  
  /* Layout */
  display: block;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  
  /* Colors - Admin Dark Theme */
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  --radius: 0.5rem;
  
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}

/* Reset all elements within admin */
.admin-isolated *,
.admin-isolated *::before,
.admin-isolated *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}

/* Block elements */
.admin-isolated div,
.admin-isolated header,
.admin-isolated nav,
.admin-isolated main,
.admin-isolated aside,
.admin-isolated section,
.admin-isolated article,
.admin-isolated footer {
  display: block;
}

/* Lists */
.admin-isolated ul,
.admin-isolated ol {
  list-style: none;
}

/* Links */
.admin-isolated a {
  color: inherit;
  text-decoration: none;
}

/* Form elements */
.admin-isolated input,
.admin-isolated button,
.admin-isolated textarea,
.admin-isolated select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background: transparent;
  border: none;
  outline: none;
}

/* Tables */
.admin-isolated table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Images */
.admin-isolated img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Headings */
.admin-isolated h1,
.admin-isolated h2,
.admin-isolated h3,
.admin-isolated h4,
.admin-isolated h5,
.admin-isolated h6 {
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  margin: 0;
}

/* Paragraphs */
.admin-isolated p {
  margin: 0;
}

/* Admin-specific utility classes */
.admin-isolated .admin-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.admin-isolated .admin-header {
  flex-shrink: 0;
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--card));
}

.admin-isolated .admin-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.admin-isolated .admin-sidebar {
  width: 250px;
  flex-shrink: 0;
  background-color: hsl(var(--card));
  border-right: 1px solid hsl(var(--border));
  overflow-y: auto;
}

.admin-isolated .admin-main {
  flex: 1;
  overflow-y: auto;
  background-color: hsl(var(--background));
}

/* Ensure no inheritance from parent styles */
.admin-isolated .admin-reset {
  all: unset;
  display: block;
}

/* Override any potential conflicts */
.admin-isolated * {
  font-family: var(--font-sans) !important;
}

/* Hide any potential main website elements */
.admin-isolated .header,
.admin-isolated .footer,
.admin-isolated .nav-menu,
.admin-isolated .main-content {
  display: none !important;
}

/* Form styling */
.admin-isolated .form-grid {
  display: grid;
  gap: 1.5rem;
}

.admin-isolated .form-section {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  padding: 1.5rem;
}

.admin-isolated .form-section-header {
  margin-bottom: 1rem;
}

.admin-isolated .form-section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: hsl(var(--foreground));
  margin-bottom: 0.25rem;
}

.admin-isolated .form-section-description {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
}

.admin-isolated .form-field {
  margin-bottom: 1rem;
}

.admin-isolated .form-field:last-child {
  margin-bottom: 0;
}

.admin-isolated .form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: hsl(var(--foreground));
  margin-bottom: 0.5rem;
}

.admin-isolated .character-count {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  margin-top: 0.25rem;
}
