'use client'

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  FileText, 
  Eye, 
  FolderOpen, 
  Users, 
  TrendingUp, 
  Plus, 
  Settings,
  Calendar,
  MoreHorizontal
} from "lucide-react"
import Link from "next/link"

const stats = [
  {
    title: "Total Posts",
    value: "24",
    change: "+2 from last month",
    icon: FileText,
  },
  {
    title: "Total Views",
    value: "12,543",
    change: "+15% from last month",
    icon: Eye,
  },
  {
    title: "Categories",
    value: "8",
    change: "+1 new category",
    icon: FolderOpen,
  },
  {
    title: "Active Users",
    value: "1,234",
    change: "+8% from last month",
    icon: Users,
  },
]

const recentPosts = [
  {
    id: 1,
    title: "501+ Best Good Night Shayari in Hindi",
    status: "published",
    views: 1234,
  },
  {
    id: 2,
    title: "501+ Best Pyar B<PERSON>",
    status: "published",
    views: 987,
  },
  {
    id: 3,
    title: "301+ Best Miss You Ya<PERSON>",
    status: "draft",
    views: 0,
  },
  {
    id: 4,
    title: "100+ Best Motivational Thoughts",
    status: "published",
    views: 2341,
  },
]

export default function AdminDashboard() {
  return (
    <div className="admin-isolated">
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground">Welcome to your blog administration panel</p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Card key={stat.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Recent Posts */}
          <Card>
            <CardHeader>
              <CardTitle className="text-foreground">Recent Posts</CardTitle>
              <CardDescription>Your latest blog posts and their performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentPosts.map((post) => (
                  <div key={post.id} className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">{post.title}</p>
                      <div className="flex items-center space-x-2">
                        <Badge 
                          variant={post.status === 'published' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {post.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {post.views} views
                        </span>
                      </div>
                    </div>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-foreground">Quick Actions</CardTitle>
              <CardDescription>Common administrative tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Link href="/admin/posts/new">
                  <Button className="w-full justify-start">
                    <Plus className="mr-2 h-4 w-4" />
                    Create New Post
                  </Button>
                </Link>
                <Link href="/admin/categories">
                  <Button variant="outline" className="w-full justify-start">
                    <FolderOpen className="mr-2 h-4 w-4" />
                    Manage Categories
                  </Button>
                </Link>
                <Link href="/admin/scheduled">
                  <Button variant="outline" className="w-full justify-start">
                    <Calendar className="mr-2 h-4 w-4" />
                    Schedule Posts
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
